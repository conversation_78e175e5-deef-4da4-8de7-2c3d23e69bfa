{"version": "0.2.0", "configurations": [{"type": "java", "name": "Main", "request": "launch", "mainClass": "fr.enedis.i2r.comsi.Main", "projectName": "comsi"}, {"type": "java", "name": "SI Mock", "request": "launch", "mainClass": "fr.enedis.i2r.si.mock.Main", "projectName": "si-mock"}, {"type": "java", "name": "i2R App", "request": "launch", "mainClass": "fr.enedis.i2r.main.Main", "projectName": "main"}, {"type": "java", "name": "Current File", "request": "launch", "mainClass": "${file}"}]}