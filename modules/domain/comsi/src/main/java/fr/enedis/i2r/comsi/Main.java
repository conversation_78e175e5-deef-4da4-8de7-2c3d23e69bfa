package fr.enedis.i2r.comsi;

import java.time.Duration;

import fr.enedis.i2r.system.watchdog.ThreadWatchdog;

public class Main {

    private volatile static int count = 0;
    private volatile static boolean running = true;
    private static ThreadWatchdog threadWatchdog;

    public static void main(String[] args) {
        threadWatchdog = new ThreadWatchdog();
        threadWatchdog.setHeartbeatInterval(Duration.ofSeconds(5));
        threadWatchdog.register(Main.class.getSimpleName());

        System.out.println("DEMO - Thread interruption - Start");
        while (running && !Thread.currentThread().isInterrupted()) {
            try {
                // Simulate some processing time
                Thread.sleep(Duration.ofSeconds(10));
                System.out.println("DEMO - Thread interruption - waitForUpdates called");

                count++;
                System.out.println("DEMO - Thread interruption - count: " + count);

                if (count > 2) {
                    System.out.println("DEMO - Thread interruption - count exceeded 2, interrupting thread");
                    Thread.currentThread().interrupt();
                    break;
                }

                // Simulate processing a configuration update
                System.out.println("DEMO - Thread interruption - simulating configuration update processing");

            } catch (InterruptedException e) {
                System.out.println("DEMO - Thread interruption - thread was interrupted during sleep");
                Thread.currentThread().interrupt();
                break;
            }
        }
        System.out.println("DEMO - Thread interruption - End");
    }

}
