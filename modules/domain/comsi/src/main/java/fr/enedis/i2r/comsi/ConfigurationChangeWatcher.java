package fr.enedis.i2r.comsi;

import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ports.DatabaseUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.si.SiConfigurationNotifierPort;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;
public class ConfigurationChangeWatcher implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationChangeWatcher.class);

    private DatabaseUpdateWatcherPort databaseUpdateWatcherPort;
    private SiConfigurationNotifierPort siNotifierPort;
    private ThreadWatchdog threadWatchdog;
    private volatile boolean running = true;

    private volatile static int count = 0;

    public ConfigurationChangeWatcher(DatabaseUpdateWatcherPort databaseUpdatePort, SiConfigurationNotifierPort siNotifierPort, ThreadWatchdog threadWatchdog) {
        this.databaseUpdateWatcherPort = databaseUpdatePort;
        this.siNotifierPort = siNotifierPort;
        this.threadWatchdog = threadWatchdog;
    }

    @Override
    public void run() {
        try {
            threadWatchdog.register(ConfigurationChangeWatcher.class.getSimpleName());
            logger.info("DEMO - Thread interruption - Start");
            while (running && !Thread.currentThread().isInterrupted()) {
                try {
                    logger.info("DEMO - Thread interruption - Processing...");
                    Thread.sleep(Duration.ofSeconds(5));

                    count++;
                    logger.info("DEMO - Thread interruption - count: " + count);

                    if (count > 2) {
                        logger.info("DEMO - Thread interruption - count exceeded 2, interrupting thread");
                        Thread.currentThread().interrupt();

                        // Throw InterruptedException to ensure ThreadWatchdog detects the interruption
                        throw new InterruptedException("Thread self-interrupted for demo");
                    }
                } catch (InterruptedException e) {
                    logger.info("DEMO - Thread interruption - thread was interrupted during sleep: {}", e.getMessage());
                    Thread.currentThread().interrupt();
                    // Re-throw to ensure the interruption is properly propagated
                    throw e;
                }
            }

            if (Thread.currentThread().isInterrupted()) {
                throw new InterruptedException("Thread was interrupted");
            }

            logger.info("DEMO - Thread interruption - End");

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Throwable e) {
            logger.error("erreur lors de l'écoute d'un changement de configuration", e);
            // For non-interruption errors, sleep to allow ThreadWatchdog to detect this thread is dead
            try {
                Thread.sleep(Duration.ofSeconds(60));
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
            }
        } finally {
            threadWatchdog.unregister(ConfigurationChangeWatcher.class.getSimpleName());
            logger.info("Configuration change watcher unregistered from ThreadWatchdog");
        }
    }

    public void stop() {
        logger.info("Stopping configuration change watcher...");
        running = false;
    }
}
