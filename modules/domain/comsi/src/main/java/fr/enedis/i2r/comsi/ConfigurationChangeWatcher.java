package fr.enedis.i2r.comsi;

import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ports.DatabaseUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.si.SiConfigurationNotifierPort;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;
public class ConfigurationChangeWatcher implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationChangeWatcher.class);

    private DatabaseUpdateWatcherPort databaseUpdateWatcherPort;
    private SiConfigurationNotifierPort siNotifierPort;
    private ThreadWatchdog threadWatchdog;
    private volatile boolean running = true;

    private static final Object lock1 = new Object();
    private static final Object lock2 = new Object();

    private volatile static int count = 0;

    public ConfigurationChangeWatcher(DatabaseUpdateWatcherPort databaseUpdatePort, SiConfigurationNotifierPort siNotifierPort, ThreadWatchdog threadWatchdog) {
        this.databaseUpdateWatcherPort = databaseUpdatePort;
        this.siNotifierPort = siNotifierPort;
        this.threadWatchdog = threadWatchdog;
    }

    @Override
    public void run() {
        try {
            threadWatchdog.register(ConfigurationChangeWatcher.class.getSimpleName());
            logger.info("Configuration change watcher started");

            // For demo testing
            demoTestThreadInterruption();
            //logger.info("DEMO - Waiting for deadlock... - Start");
            //Thread.sleep(Duration.ofSeconds(120));
            //logger.info("DEMO - Waiting for deadlock... - End");
            //demoTestDeadlock();

        } catch (Throwable e) {
            logger.error("erreur lors de l'écoute d'un changement de configuration", e);
            // Sleep to allow ThreadWatchdog to detect this thread is dead
            try {
                Thread.sleep(Duration.ofSeconds(60));
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
            }
        }
    }


    public void demoTestThreadInterruption() {
        logger.info("DEMO - Thread interruption - Start");
        while (running && !Thread.currentThread().isInterrupted()) {
            try {
                // Simulate some processing time
                Thread.sleep(Duration.ofSeconds(10));
                logger.info("DEMO - Thread interruption - waitForUpdates called");

                count++;
                logger.info("DEMO - Thread interruption - count: " + count);

                if (count > 2) {
                    logger.info("DEMO - Thread interruption - count exceeded 2, interrupting thread");
                    Thread.currentThread().interrupt();
                    break;
                }

                // Simulate processing a configuration update
                logger.info("DEMO - Thread interruption - simulating configuration update processing");

            } catch (InterruptedException e) {
                logger.info("DEMO - Thread interruption - thread was interrupted during sleep");
                Thread.currentThread().interrupt();
                break;
            }
        }
        logger.info("DEMO - Thread interruption - End");
    }

    public void demoTestDeadlock() {
        logger.info("DEMO - Deadlock - Start");

        // Thread 1: locks in order lock1 -> lock2
        Thread thread1 = new Thread(() -> {
            logger.info("DEMO - Deadlock - Thread1 starting");
            synchronized(lock1) {
                logger.info("DEMO - Deadlock - Thread1 acquired lock1");
                try {
                    // Give thread2 time to acquire lock2
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }

                logger.info("DEMO - Deadlock - Thread1 trying to acquire lock2");
                synchronized(lock2) {
                    logger.info("DEMO - Deadlock - Thread1 acquired lock2 (this should not print if deadlocked)");
                }
            }
            logger.info("DEMO - Deadlock - Thread1 finished");
        }, "DeadlockDemo-Thread1");

        // Thread 2: locks in reverse order lock2 -> lock1
        Thread thread2 = new Thread(() -> {
            logger.info("DEMO - Deadlock - Thread2 starting");
            synchronized(lock2) {
                logger.info("DEMO - Deadlock - Thread2 acquired lock2");
                try {
                    // Give thread1 time to acquire lock1
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }

                logger.info("DEMO - Deadlock - Thread2 trying to acquire lock1");
                synchronized(lock1) {
                    logger.info("DEMO - Deadlock - Thread2 acquired lock1 (this should not print if deadlocked)");
                }
            }
            logger.info("DEMO - Deadlock - Thread2 finished");
        }, "DeadlockDemo-Thread2");

        thread1.start();
        thread2.start();

        logger.info("DEMO - Deadlock - Both threads started, deadlock should occur");

        // Wait a bit to let the deadlock happen
        try {
            Thread.sleep(2000);
            logger.info("DEMO - Deadlock - Waited 2 seconds, threads should be deadlocked now");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        logger.info("DEMO - Deadlock - End");
    }

    public void stop() {
        logger.info("Stopping configuration change watcher...");
        running = false;
    }
}
