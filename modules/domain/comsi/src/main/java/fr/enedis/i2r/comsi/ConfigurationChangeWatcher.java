package fr.enedis.i2r.comsi;

import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ports.DatabaseUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.si.SiConfigurationNotifierPort;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;
public class ConfigurationChangeWatcher implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationChangeWatcher.class);

    private DatabaseUpdateWatcherPort databaseUpdateWatcherPort;
    private SiConfigurationNotifierPort siNotifierPort;
    private ThreadWatchdog threadWatchdog;
    private volatile boolean running = true;

    private static final Object lock1 = new Object();
    private static final Object lock2 = new Object();

    private volatile static int count = 0;

    public ConfigurationChangeWatcher(DatabaseUpdateWatcherPort databaseUpdatePort, SiConfigurationNotifierPort siNotifierPort, ThreadWatchdog threadWatchdog) {
        this.databaseUpdateWatcherPort = databaseUpdatePort;
        this.siNotifierPort = siNotifierPort;
        this.threadWatchdog = threadWatchdog;
    }

    @Override
    public void run() {
        try {
            threadWatchdog.register(ConfigurationChangeWatcher.class.getSimpleName());
            logger.info("Configuration change watcher started");

            // For demo testing
            demoTestThreadInterruption();
            //logger.info("DEMO - Waiting for deadlock... - Start");
            //Thread.sleep(Duration.ofSeconds(120));
            //logger.info("DEMO - Waiting for deadlock... - End");
            //demoTestDeadlock();

        } catch (InterruptedException e) {
            logger.info("Configuration change watcher was interrupted: {}", e.getMessage());

            // Signal to ThreadWatchdog immediately for fastest detection
            threadWatchdog.signalThreadTerminating(ConfigurationChangeWatcher.class.getSimpleName(),
                "Thread interrupted: " + e.getMessage());

            Thread.currentThread().interrupt();
            // Don't sleep here - let the thread terminate immediately so ThreadWatchdog can detect it
            logger.info("Configuration change watcher thread terminating due to interruption");
        } catch (Throwable e) {
            logger.error("erreur lors de l'écoute d'un changement de configuration", e);
            // For non-interruption errors, sleep to allow ThreadWatchdog to detect this thread is dead
            try {
                Thread.sleep(Duration.ofSeconds(60));
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                logger.info("Configuration change watcher interrupted during error handling");
            }
        } finally {
            // Unregister from watchdog when thread is about to terminate
            threadWatchdog.unregister(ConfigurationChangeWatcher.class.getSimpleName());
            logger.info("Configuration change watcher unregistered from ThreadWatchdog");
        }
    }


    public void demoTestThreadInterruption() throws InterruptedException {
        logger.info("DEMO - Thread interruption - Start");
        while (running && !Thread.currentThread().isInterrupted()) {
            try {
                // Simulate some processing time
                Thread.sleep(Duration.ofSeconds(10));
                logger.info("DEMO - Thread interruption - waitForUpdates called");

                count++;
                logger.info("DEMO - Thread interruption - count: " + count);

                if (count > 2) {
                    logger.info("DEMO - Thread interruption - count exceeded 2, interrupting thread");

                    // Signal to ThreadWatchdog immediately for fastest detection
                    threadWatchdog.signalThreadTerminating(ConfigurationChangeWatcher.class.getSimpleName(),
                        "Demo thread interruption - count exceeded 2");

                    Thread.currentThread().interrupt();

                    // Throw InterruptedException to ensure ThreadWatchdog detects the interruption
                    logger.info("DEMO - Thread interruption - throwing InterruptedException for ThreadWatchdog detection");
                    throw new InterruptedException("Thread self-interrupted for demo");
                }

                // Simulate processing a configuration update
                logger.info("DEMO - Thread interruption - simulating configuration update processing");

            } catch (InterruptedException e) {
                logger.info("DEMO - Thread interruption - thread was interrupted during sleep: {}", e.getMessage());
                Thread.currentThread().interrupt();
                // Re-throw to ensure the interruption is properly propagated
                throw e;
            }
        }

        // If we exit the loop due to interruption, throw InterruptedException
        if (Thread.currentThread().isInterrupted()) {
            logger.info("DEMO - Thread interruption - loop exited due to interruption, throwing InterruptedException");
            throw new InterruptedException("Thread was interrupted");
        }

        logger.info("DEMO - Thread interruption - End");
    }



    public void stop() {
        logger.info("Stopping configuration change watcher...");
        running = false;
    }
}
