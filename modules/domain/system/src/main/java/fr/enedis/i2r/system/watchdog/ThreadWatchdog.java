package fr.enedis.i2r.system.watchdog;

import java.time.Duration;
import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.system.systemd.WatchdogService;

public class ThreadWatchdog {

    private final Logger logger = LoggerFactory.getLogger(ThreadWatchdog.class);

    private static class ThreadInfo {
        final Thread thread;
        volatile Instant lastHeartbeat;

        ThreadInfo(Thread thread, Instant lastHeartbeat) {
            this.thread = thread;
            this.lastHeartbeat = lastHeartbeat;
        }
    }

    private final ConcurrentMap<String, ThreadInfo> monitoredThreads = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, ScheduledFuture<?>> scheduledHeartbeats = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler;
    private Optional<ScheduledFuture<?>> timeoutChecker = Optional.empty();
    private Optional<WatchdogService> watchdogService = Optional.empty();

    private Duration heartbeatInterval = Duration.ofSeconds(45);
    private Duration threadTimeout = heartbeatInterval.multipliedBy(2);

    public ThreadWatchdog() {
        this(Executors.newScheduledThreadPool(5));
    }

    public ThreadWatchdog(ScheduledExecutorService scheduler) {
        this.scheduler = scheduler;
    }

    public void setHeartbeatInterval(Duration interval) {
        this.heartbeatInterval = interval;
        this.threadTimeout = heartbeatInterval.multipliedBy(2);
    }

    public void setWatchdogService(WatchdogService watchdogService) {
        this.watchdogService = Optional.ofNullable(watchdogService);
    }

    public void register(String threadName) {
        Thread currentThread = Thread.currentThread();
        monitoredThreads.put(threadName, new ThreadInfo(currentThread, Instant.now()));
        startHeartbeat(threadName);
        startTimeoutChecker();
        logger.info("Thread registered for monitoring: {} (Thread: {})", threadName, currentThread.getName());
    }

    private void startTimeoutChecker() {
        if (timeoutChecker.isEmpty() || timeoutChecker.get().isCancelled()) {
            ScheduledFuture<?> checker = scheduler.scheduleAtFixedRate(
                this::checkForDeadThreads,
                threadTimeout.toMillis(),
                heartbeatInterval.toMillis(),
                TimeUnit.MILLISECONDS
            );
            timeoutChecker = Optional.of(checker);
            logger.info("Thread timeout checker started with {}ms timeout, checking every {}ms",
                threadTimeout.toMillis(), heartbeatInterval.toMillis());
        }
    }

    private void checkForDeadThreads() {
        Instant now = Instant.now();
        monitoredThreads.entrySet().removeIf(entry -> {
            String threadName = entry.getKey();
            ThreadInfo threadInfo = entry.getValue();
            Thread monitoredThread = threadInfo.thread;
            Instant lastHeartbeat = threadInfo.lastHeartbeat;
            Duration timeSinceLastHeartbeat = Duration.between(lastHeartbeat, now);

            if (timeSinceLastHeartbeat.compareTo(threadTimeout) > 0
                    || monitoredThread.isInterrupted()
                    || monitoredThread.getState() == Thread.State.TERMINATED) {
                String threadInfoStr = String.format("(Thread: %s, State: %s, Interrupted: %s)",
                    monitoredThread.getName(), monitoredThread.getState(), monitoredThread.isInterrupted());

                logger.error("CRITICAL - DEAD THREAD DETECTED: {} {} - Last heartbeat was {}s ago (timeout: {}s) - "
                    + "Triggering system exit to restart service",
                    threadName, threadInfoStr, timeSinceLastHeartbeat.toSeconds(), threadTimeout.toSeconds());

                System.exit(1);
                return true; // Remove from threads map
            }
            return false; // Keep in threads map
        });
    }

    private void startHeartbeat(String threadName) {
        ScheduledFuture<?> future = scheduler.scheduleAtFixedRate(
            () -> heartbeat(threadName),
            heartbeatInterval.toMillis(),
            heartbeatInterval.toMillis(),
            TimeUnit.MILLISECONDS
        );
        scheduledHeartbeats.put(threadName, future);
        logger.info("Heartbeat started for thread: {} with interval: {}ms", threadName, heartbeatInterval.toMillis());
    }

    private void heartbeat(String threadName) {
        ThreadInfo threadInfo = monitoredThreads.get(threadName);

        if (threadInfo != null) {
            Thread monitoredThread = threadInfo.thread;
            // Check if the monitored thread is interrupted or terminated
            if (monitoredThread.isInterrupted() || monitoredThread.getState() == Thread.State.TERMINATED) {
                logger.warn("Thread {} is interrupted/terminated - stopping heartbeat updates", threadName);
                return; // Don't update timestamp for interrupted threads
            }

            threadInfo.lastHeartbeat = Instant.now();
            logger.debug("Heartbeat recorded for thread: {} at {} (State: {})", threadName, threadInfo.lastHeartbeat, monitoredThread.getState());
        }
    }

    public void unregister(String threadName) {
        ThreadInfo removed = monitoredThreads.remove(threadName);
        ScheduledFuture<?> future = scheduledHeartbeats.remove(threadName);
        if (future != null) {
            future.cancel(false);
            logger.info("Heartbeat stopped for thread: {}", threadName);
        }
        if (removed != null) {
            logger.info("Thread unregistered from monitoring: {} (was: {})", threadName, removed.thread.getName());
        }
    }



    public void shutdown() {
        monitoredThreads.clear();
        scheduledHeartbeats.values().forEach(future -> future.cancel(false));
        scheduledHeartbeats.clear();

        // Stop timeout checker if running
        timeoutChecker.ifPresent(future -> {
            if (!future.isCancelled()) {
                future.cancel(false);
                logger.debug("Timeout checker stopped");
            }
        });
        timeoutChecker = Optional.empty();

        logger.debug("All threads cleared from monitoring");
        scheduler.shutdown();
        logger.info("ThreadWatchdog scheduler shutdown");
    }
}
