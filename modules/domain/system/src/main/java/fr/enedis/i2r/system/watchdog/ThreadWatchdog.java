package fr.enedis.i2r.system.watchdog;

import java.time.Duration;
import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.system.systemd.WatchdogService;

public class ThreadWatchdog {

    private final Logger logger = LoggerFactory.getLogger(ThreadWatchdog.class);

    private final ConcurrentMap<String, Instant> threads = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, Thread> threadReferences = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, ScheduledFuture<?>> scheduledHeartbeats = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler;
    private Optional<ScheduledFuture<?>> timeoutChecker = Optional.empty();
    private Optional<WatchdogService> watchdogService = Optional.empty();

    private Duration heartbeatInterval = Duration.ofSeconds(45);
    private Duration threadTimeout = heartbeatInterval.multipliedBy(2);

    public ThreadWatchdog() {
        this(Executors.newScheduledThreadPool(5));
    }

    public ThreadWatchdog(ScheduledExecutorService scheduler) {
        this.scheduler = scheduler;
    }

    public void setHeartbeatInterval(Duration interval) {
        this.heartbeatInterval = interval;
        this.threadTimeout = heartbeatInterval.multipliedBy(2);
    }

    public void setWatchdogService(WatchdogService watchdogService) {
        this.watchdogService = Optional.ofNullable(watchdogService);
    }

    public void register(String threadName) {
        Thread currentThread = Thread.currentThread();
        threads.put(threadName, Instant.now());
        threadReferences.put(threadName, currentThread);
        startHeartbeat(threadName);
        startTimeoutChecker();
        logger.info("Thread registered for monitoring: {} (Thread: {})", threadName, currentThread.getName());
    }

    private void startTimeoutChecker() {
        if (timeoutChecker.isEmpty() || timeoutChecker.get().isCancelled()) {
            // Check more frequently for faster detection - every 2 seconds or half heartbeat interval, whichever is smaller
            long checkIntervalMs = Math.min(2000, heartbeatInterval.toMillis() / 2);

            ScheduledFuture<?> checker = scheduler.scheduleAtFixedRate(
                this::checkForDeadThreads,
                checkIntervalMs, // Start checking immediately after first interval
                checkIntervalMs,
                TimeUnit.MILLISECONDS
            );
            timeoutChecker = Optional.of(checker);
            logger.info("Thread timeout checker started with {}ms timeout, checking every {}ms (aggressive mode)",
                threadTimeout.toMillis(), checkIntervalMs);
        }
    }

    private void checkForDeadThreads() {
        Instant now = Instant.now();
        threads.entrySet().removeIf(entry -> {
            String threadName = entry.getKey();
            Thread monitoredThread = threadReferences.get(threadName);
            Instant lastHeartbeat = entry.getValue();
            Duration timeSinceLastHeartbeat = Duration.between(lastHeartbeat, now);

            // More aggressive detection of dead/problematic threads
            boolean isThreadDead = false;
            String reason = "";

            if (monitoredThread == null) {
                isThreadDead = true;
                reason = "Thread reference is null";
            } else if (monitoredThread.getState() == Thread.State.TERMINATED) {
                isThreadDead = true;
                reason = "Thread is TERMINATED";
            } else if (monitoredThread.isInterrupted()) {
                isThreadDead = true;
                reason = "Thread is INTERRUPTED";
            } else if (timeSinceLastHeartbeat.compareTo(threadTimeout) > 0) {
                isThreadDead = true;
                reason = String.format("Heartbeat timeout (last: %ds ago, timeout: %ds)",
                    timeSinceLastHeartbeat.toSeconds(), threadTimeout.toSeconds());
            } else if (monitoredThread.getState() == Thread.State.BLOCKED) {
                // Check if thread has been blocked for too long
                if (timeSinceLastHeartbeat.compareTo(threadTimeout.dividedBy(2)) > 0) {
                    isThreadDead = true;
                    reason = String.format("Thread BLOCKED for too long (%ds)", timeSinceLastHeartbeat.toSeconds());
                }
            } else if (monitoredThread.getState() == Thread.State.WAITING ||
                       monitoredThread.getState() == Thread.State.TIMED_WAITING) {
                // Check if thread has been waiting for too long without heartbeat
                if (timeSinceLastHeartbeat.compareTo(threadTimeout) > 0) {
                    isThreadDead = true;
                    reason = String.format("Thread %s for too long without heartbeat (%ds)",
                        monitoredThread.getState(), timeSinceLastHeartbeat.toSeconds());
                }
            }

            if (isThreadDead) {
                String threadInfo = monitoredThread != null ?
                    String.format("(Thread: %s, State: %s, Interrupted: %s, Alive: %s)",
                        monitoredThread.getName(), monitoredThread.getState(),
                        monitoredThread.isInterrupted(), monitoredThread.isAlive()) :
                    "(Thread reference not found)";

                logger.error("CRITICAL - DEAD THREAD DETECTED: {} {} - Reason: {} - "
                    + "Triggering system exit to restart service",
                    threadName, threadInfo, reason);

                // Immediate system exit for faster detection
                new Thread(() -> {
                    try {
                        Thread.sleep(100); // Brief delay to ensure log is written
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    logger.error("SYSTEM EXIT - Dead thread: {}", threadName);
                    System.exit(1);
                }).start();

                return true; // Remove from threads map
            }
            return false; // Keep in threads map
        });
    }

    private void startHeartbeat(String threadName) {
        ScheduledFuture<?> future = scheduler.scheduleAtFixedRate(
            () -> heartbeat(threadName),
            heartbeatInterval.toMillis(),
            heartbeatInterval.toMillis(),
            TimeUnit.MILLISECONDS
        );
        scheduledHeartbeats.put(threadName, future);
        logger.info("Heartbeat started for thread: {} with interval: {}ms", threadName, heartbeatInterval.toMillis());
    }

    private void heartbeat(String threadName) {
        Instant lastHeartbeat = threads.get(threadName);
        Thread monitoredThread = threadReferences.get(threadName);

        if (lastHeartbeat != null && monitoredThread != null) {
            // More aggressive checking - stop heartbeats immediately for any problematic state
            if (monitoredThread.isInterrupted() ||
                monitoredThread.getState() == Thread.State.TERMINATED ||
                !monitoredThread.isAlive()) {

                logger.warn("Thread {} is in problematic state (Interrupted: {}, State: {}, Alive: {}) - stopping heartbeat updates",
                    threadName, monitoredThread.isInterrupted(), monitoredThread.getState(), monitoredThread.isAlive());

                // Cancel the heartbeat for this thread to prevent further updates
                ScheduledFuture<?> future = scheduledHeartbeats.get(threadName);
                if (future != null) {
                    future.cancel(false);
                    logger.info("Heartbeat cancelled for problematic thread: {}", threadName);
                }
                return; // Don't update timestamp for problematic threads
            }

            threads.put(threadName, Instant.now());
            logger.debug("Heartbeat recorded for thread: {} at {} (State: {}, Alive: {})",
                threadName, Instant.now(), monitoredThread.getState(), monitoredThread.isAlive());
        } else {
            logger.warn("Heartbeat attempted for thread {} but lastHeartbeat={}, monitoredThread={}",
                threadName, lastHeartbeat, monitoredThread);
        }
    }

    public void unregister(String threadName) {
        Instant removed = threads.remove(threadName);
        Thread threadRef = threadReferences.remove(threadName);
        ScheduledFuture<?> future = scheduledHeartbeats.remove(threadName);
        if (future != null) {
            future.cancel(false);
            logger.info("Heartbeat stopped for thread: {}", threadName);
        }
        if (removed != null) {
            logger.info("Thread unregistered from monitoring: {} (was: {})", threadName,
                threadRef != null ? threadRef.getName() : "unknown");
        }
    }

    public void signalThreadTerminating(String threadName, String reason) {
        Thread monitoredThread = threadReferences.get(threadName);
        String threadInfo = monitoredThread != null ?
            String.format("(Thread: %s, State: %s, Interrupted: %s, Alive: %s)",
                monitoredThread.getName(), monitoredThread.getState(),
                monitoredThread.isInterrupted(), monitoredThread.isAlive()) :
            "(Thread reference not found)";

        logger.error("CRITICAL - THREAD TERMINATION SIGNAL: {} {} - Reason: {} - "
            + "Triggering immediate system exit",
            threadName, threadInfo, reason);

        // Immediate system exit for fastest response
        new Thread(() -> {
            try {
                Thread.sleep(50); // Very brief delay to ensure log is written
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            logger.error("SYSTEM EXIT - Thread termination signal: {} - {}", threadName, reason);
            System.exit(1);
        }).start();
    }

    public void shutdown() {
        threads.clear();
        threadReferences.clear();
        scheduledHeartbeats.values().forEach(future -> future.cancel(false));
        scheduledHeartbeats.clear();

        // Stop timeout checker if running
        timeoutChecker.ifPresent(future -> {
            if (!future.isCancelled()) {
                future.cancel(false);
                logger.debug("Timeout checker stopped");
            }
        });
        timeoutChecker = Optional.empty();

        logger.debug("All threads cleared from monitoring");
        scheduler.shutdown();
        logger.info("ThreadWatchdog scheduler shutdown");
    }

    public ConcurrentMap<String, Instant> getThreads() {
        return threads;
    }
}
